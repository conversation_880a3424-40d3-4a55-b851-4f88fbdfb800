// API Service with enhanced error handling and retry logic
import mockApiService from './mockApiService';
import cacheService from './cacheService';
import csrfService from './csrfService';

const API_BASE_URL = process.env.REACT_APP_API_URL || '';

// Cache configuration
const DEFAULT_CACHE_ENABLED = true;
const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Use mock API service in development mode
const USE_MOCK_API = false; // Set to false to use real API

// Configurable fetch with timeout
export const fetchWithTimeout = async (url, options = {}, timeout = 8000) => {
  const controller = new AbortController();
  const { signal } = controller;

  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // Get CSRF headers for non-GET requests
    let headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (options.method && options.method !== 'GET') {
      try {
        const csrfHeaders = await csrfService.getHeaders();
        headers = { ...headers, ...csrfHeaders };
      } catch (error) {
        console.warn('Failed to get CSRF token:', error);
      }
    }

    const response = await fetch(url, {
      ...options,
      signal,
      headers,
      credentials: 'include', // Include cookies for CSRF
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);

    if (error.name === 'AbortError') {
      throw new Error(`Request timeout after ${timeout}ms`);
    }

    throw error;
  }
};

// Retry logic with exponential backoff
export const retryWithBackoff = async (fetchFn, maxRetries = 3, baseDelay = 1000) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fetchFn();
    } catch (error) {
      console.warn(`API request failed (attempt ${attempt + 1}/${maxRetries + 1}):`, error.message);
      lastError = error;

      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

// API endpoints
export const api = {
  // Health check
  checkStatus: async () => {
    if (USE_MOCK_API) {
      return mockApiService.checkStatus();
    }

    try {
      return await fetchWithTimeout(`${API_BASE_URL}/api/status/`);
    } catch (error) {
      console.warn('Status check failed, using mock data:', error.message);
      // Return mock data when backend is unavailable
      return {
        status: 'ok',
        message: 'Mock status response',
        offline: true
      };
    }
  },

  // Get app data with improved caching and offline support
  getAppData: async () => {
    if (USE_MOCK_API) {
      return mockApiService.getAppData();
    }

    // Check cache first
    const cacheKey = 'app_data';
    const cachedData = cacheService.getItem(cacheKey);

    if (cachedData) {
      console.log('Using cached app data');

      // Refresh cache in background
      retryWithBackoff(() => fetchWithTimeout(`${API_BASE_URL}/api/app-data/`))
        .then(freshData => {
          cacheService.setItem(cacheKey, freshData, {
            duration: DEFAULT_CACHE_DURATION,
            persist: true
          });
        })
        .catch(error => console.warn('Background refresh failed:', error));

      return cachedData;
    }

    try {
      const data = await retryWithBackoff(() =>
        fetchWithTimeout(`${API_BASE_URL}/api/app-data/`)
      );

      // Cache successful response
      cacheService.setItem(cacheKey, data, {
        duration: DEFAULT_CACHE_DURATION,
        persist: true
      });

      return data;
    } catch (error) {
      console.warn('App data fetch failed, using mock data:', error.message);
      return {
        app: {
          name: 'Demo App',
          components: [],
          offline: true
        }
      };
    }
  },

  // Generic request method
  request: async (endpoint, method = 'GET', data = null, options = {}) => {
    const { cache = DEFAULT_CACHE_ENABLED, cacheDuration = DEFAULT_CACHE_DURATION } = options;

    if (USE_MOCK_API) {
      return mockApiService.request(endpoint, method, data);
    }

    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}/${endpoint}`;

    // Check cache for GET requests
    const cacheKey = `api_${url}`;
    if (method === 'GET' && cache) {
      const cachedData = cacheService.getItem(cacheKey);
      if (cachedData) {
        console.log(`Using cached data for ${url}`);
        return cachedData;
      }
    }

    const fetchOptions = {
      method,
      ...options,
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      fetchOptions.body = JSON.stringify(data);
    }

    try {
      const result = await retryWithBackoff(() =>
        fetchWithTimeout(url, fetchOptions)
      );

      // Cache GET responses
      if (method === 'GET' && cache) {
        cacheService.setItem(cacheKey, result, {
          duration: cacheDuration,
          persist: options.persistCache || false
        });
      }

      return result;
    } catch (error) {
      console.warn(`API request to ${url} failed:`, error.message);

      // For development, return mock data to prevent UI crashes
      if (process.env.NODE_ENV === 'development') {
        console.info('Returning mock data for development');
        return { success: true, message: 'Mock response', data: {} };
      }

      throw error;
    }
  },

  // Clear API cache
  clearCache: (endpoint = null) => {
    if (endpoint) {
      const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}/${endpoint}`;
      const cacheKey = `api_${url}`;
      return cacheService.removeItem(cacheKey);
    } else {
      return cacheService.clearCache();
    }
  }
};

export default api;


