# backend/my_app/csrf_middleware.py
from django.middleware.csrf import CsrfViewMiddleware
from django.utils.deprecation import MiddlewareMixin


class CustomCsrfMiddleware(CsrfViewMiddleware):
    """
    Custom CSRF middleware that exempts certain API endpoints from CSRF protection.
    """
    
    def process_view(self, request, callback, callback_args, callback_kwargs):
        # List of URL patterns to exempt from CSRF protection
        exempt_patterns = [
            '/api/errors/',
            '/graphql/',
            '/api/csrf-token/',  # Allow CSRF token endpoint without CSRF protection
            '/api/health/',      # Allow health checks without CSRF protection
            '/api/status/',      # Allow status checks without CSRF protection
        ]

        # Check if the request path matches any exempt pattern
        for pattern in exempt_patterns:
            if request.path.startswith(pattern):
                return None  # Skip CSRF protection

        # Apply normal CSRF protection for other endpoints
        return super().process_view(request, callback, callback_args, callback_kwargs)
