# Environment Configuration Example
# Copy this file to .env and modify the values as needed

# Database Configuration
# Set to 'true' to use PostgreSQL, 'false' to use SQLite
USE_POSTGRES=false

# PostgreSQL Configuration (only used if USE_POSTGRES=true)
POSTGRES_DB=myapp
POSTGRES_USER=myappuser
POSTGRES_PASSWORD=myapppassword

# Django Configuration
DJANGO_SECRET_KEY=your-secret-key-here
DJANGO_DEBUG=true
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1,backend,frontend

# API Keys (optional)
OPENAI_API_KEY=your-openai-api-key-here
AI_STUDIO_API_KEY=your-ai-studio-key-here
STABILITY_API_KEY=your-stability-api-key

# Frontend Configuration
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_API_URL=
REACT_APP_BACKEND_HOST=backend

# WebSocket Configuration
WEBSOCKET_TARGET=ws://backend:8765

# Development Settings
NODE_ENV=development
CHOKIDAR_USEPOLLING=true