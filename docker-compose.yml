services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/usr/src/app:cached
    command: [ "python", "manage.py", "runserver", "0.0.0.0:8000" ]
    environment:
      - DJANGO_SETTINGS_MODULE=app_builder_201.settings
      - USE_POSTGRES=${USE_POSTGRES:-true}
      - POSTGRES_DB=myapp
      - POSTGRES_USER=myappuser
      - POSTGRES_PASSWORD=myapppassword
    depends_on:
      - db
    ports:
      - "8000:8000"
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_BASE_URL=http://localhost:8000
      - REACT_APP_API_URL=
      - REACT_APP_BACKEND_HOST=backend
      - API_TARGET=http://backend:8000
      - WEBSOCKET_TARGET=ws://backend:8765
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: myappuser
      POSTGRES_PASSWORD: myapppassword
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U myappuser -d myapp" ]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  frontend_node_modules:
